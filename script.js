class TodoApp {
    constructor() {
        this.todos = this.loadTodos();
        this.currentFilter = 'all';
        this.editingId = null;
        
        this.initializeElements();
        this.bindEvents();
        this.render();
    }

    initializeElements() {
        this.todoForm = document.getElementById('addTodoForm');
        this.todoInput = document.getElementById('todoInput');
        this.todoList = document.getElementById('todoList');
        this.emptyState = document.getElementById('emptyState');
        this.todoCount = document.getElementById('todoCount');
        this.clearCompletedBtn = document.getElementById('clearCompleted');
        this.filterBtns = document.querySelectorAll('.filter-btn');
        this.todoTemplate = document.getElementById('todoTemplate');
    }

    bindEvents() {
        // Add todo form submission
        this.todoForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.addTodo();
        });

        // Filter buttons
        this.filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });

        // Clear completed button
        this.clearCompletedBtn.addEventListener('click', () => {
            this.clearCompleted();
        });

        // Todo list event delegation
        this.todoList.addEventListener('click', (e) => {
            const todoItem = e.target.closest('.todo-item');
            if (!todoItem) return;

            const todoId = parseInt(todoItem.dataset.id);

            if (e.target.closest('.toggle-btn')) {
                this.toggleTodo(todoId);
            } else if (e.target.closest('.delete-btn')) {
                this.deleteTodo(todoId);
            } else if (e.target.closest('.edit-btn')) {
                this.startEdit(todoId);
            }
        });

        // Handle edit input
        this.todoList.addEventListener('keydown', (e) => {
            if (e.target.classList.contains('edit-input')) {
                if (e.key === 'Enter') {
                    this.saveEdit(parseInt(e.target.closest('.todo-item').dataset.id), e.target.value);
                } else if (e.key === 'Escape') {
                    this.cancelEdit();
                }
            }
        });

        this.todoList.addEventListener('blur', (e) => {
            if (e.target.classList.contains('edit-input')) {
                this.saveEdit(parseInt(e.target.closest('.todo-item').dataset.id), e.target.value);
            }
        }, true);
    }

    addTodo() {
        const text = this.todoInput.value.trim();
        if (!text) return;

        const todo = {
            id: Date.now(),
            text: text,
            completed: false,
            createdAt: new Date().toISOString()
        };

        this.todos.unshift(todo);
        this.todoInput.value = '';
        this.saveTodos();
        this.render();

        // Add animation class
        setTimeout(() => {
            const newItem = this.todoList.querySelector(`[data-id="${todo.id}"]`);
            if (newItem) {
                newItem.style.animation = 'none';
                newItem.offsetHeight; // Trigger reflow
                newItem.style.animation = 'fadeIn 0.4s ease-out';
            }
        }, 10);
    }

    toggleTodo(id) {
        const todo = this.todos.find(t => t.id === id);
        if (todo) {
            todo.completed = !todo.completed;
            this.saveTodos();
            this.render();
        }
    }

    deleteTodo(id) {
        const todoItem = this.todoList.querySelector(`[data-id="${id}"]`);
        if (todoItem) {
            todoItem.style.animation = 'fadeOut 0.3s ease-out';
            setTimeout(() => {
                this.todos = this.todos.filter(t => t.id !== id);
                this.saveTodos();
                this.render();
            }, 300);
        }
    }

    startEdit(id) {
        this.cancelEdit(); // Cancel any existing edit
        
        const todoItem = this.todoList.querySelector(`[data-id="${id}"]`);
        const todoText = todoItem.querySelector('.todo-text');
        const editInput = todoItem.querySelector('.edit-input');
        
        this.editingId = id;
        todoText.style.display = 'none';
        editInput.style.display = 'block';
        editInput.value = todoText.textContent;
        editInput.focus();
        editInput.select();
    }

    saveEdit(id, newText) {
        const text = newText.trim();
        if (!text) {
            this.deleteTodo(id);
            return;
        }

        const todo = this.todos.find(t => t.id === id);
        if (todo) {
            todo.text = text;
            this.saveTodos();
        }
        
        this.cancelEdit();
        this.render();
    }

    cancelEdit() {
        if (this.editingId) {
            const todoItem = this.todoList.querySelector(`[data-id="${this.editingId}"]`);
            if (todoItem) {
                const todoText = todoItem.querySelector('.todo-text');
                const editInput = todoItem.querySelector('.edit-input');
                
                todoText.style.display = 'block';
                editInput.style.display = 'none';
            }
            this.editingId = null;
        }
    }

    setFilter(filter) {
        this.currentFilter = filter;
        
        // Update active filter button
        this.filterBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.filter === filter);
        });
        
        this.render();
    }

    clearCompleted() {
        this.todos = this.todos.filter(todo => !todo.completed);
        this.saveTodos();
        this.render();
    }

    getFilteredTodos() {
        switch (this.currentFilter) {
            case 'active':
                return this.todos.filter(todo => !todo.completed);
            case 'completed':
                return this.todos.filter(todo => todo.completed);
            default:
                return this.todos;
        }
    }

    render() {
        const filteredTodos = this.getFilteredTodos();
        
        // Clear the list
        this.todoList.innerHTML = '';
        
        // Show/hide empty state
        if (filteredTodos.length === 0) {
            this.emptyState.classList.remove('hidden');
        } else {
            this.emptyState.classList.add('hidden');
            
            // Render todos
            filteredTodos.forEach(todo => {
                const todoElement = this.createTodoElement(todo);
                this.todoList.appendChild(todoElement);
            });
        }
        
        // Update stats
        this.updateStats();
        
        // Update clear completed button
        const completedCount = this.todos.filter(todo => todo.completed).length;
        this.clearCompletedBtn.disabled = completedCount === 0;
    }

    createTodoElement(todo) {
        const template = this.todoTemplate.content.cloneNode(true);
        const todoItem = template.querySelector('.todo-item');
        const todoText = template.querySelector('.todo-text');
        
        todoItem.dataset.id = todo.id;
        todoText.textContent = todo.text;
        
        if (todo.completed) {
            todoItem.classList.add('completed');
        }
        
        return template;
    }

    updateStats() {
        const activeCount = this.todos.filter(todo => !todo.completed).length;
        const totalCount = this.todos.length;
        
        let statsText;
        if (totalCount === 0) {
            statsText = '0 items';
        } else if (this.currentFilter === 'active') {
            statsText = `${activeCount} active`;
        } else if (this.currentFilter === 'completed') {
            statsText = `${totalCount - activeCount} completed`;
        } else {
            statsText = `${totalCount} total, ${activeCount} active`;
        }
        
        this.todoCount.textContent = statsText;
    }

    saveTodos() {
        localStorage.setItem('todos', JSON.stringify(this.todos));
    }

    loadTodos() {
        try {
            const saved = localStorage.getItem('todos');
            return saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.error('Error loading todos:', error);
            return [];
        }
    }
}

// Add fadeOut animation to CSS dynamically
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeOut {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(-20px);
        }
    }
`;
document.head.appendChild(style);

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new TodoApp();
});

// Add some sample todos for demonstration (only if no todos exist)
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        const savedTodos = localStorage.getItem('todos');
        if (!savedTodos || JSON.parse(savedTodos).length === 0) {
            const sampleTodos = [
                { id: 1, text: 'Welcome to your beautiful todo app! 🎉', completed: false, createdAt: new Date().toISOString() },
                { id: 2, text: 'Click the circle to mark items as complete', completed: false, createdAt: new Date().toISOString() },
                { id: 3, text: 'Use the edit button to modify todos', completed: false, createdAt: new Date().toISOString() },
                { id: 4, text: 'This is a completed todo', completed: true, createdAt: new Date().toISOString() }
            ];
            localStorage.setItem('todos', JSON.stringify(sampleTodos));
            location.reload();
        }
    }, 100);
});
