/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 2rem 0;
    text-align: center;
}

.header-content {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 1rem;
}

.app-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.app-title i {
    color: #4ade80;
}

.app-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    font-weight: 300;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem 1rem;
    display: flex;
    justify-content: center;
}

.todo-container {
    width: 100%;
    max-width: 600px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Add Todo Section */
.add-todo-section {
    padding: 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e2e8f0;
}

.input-group {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.todo-input {
    flex: 1;
    padding: 1rem 1.25rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    transition: all 0.3s ease;
    background: white;
}

.todo-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.add-btn {
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.add-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

/* Filter Section */
.filter-section {
    padding: 1.5rem 2rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 2px solid #e2e8f0;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-btn:hover {
    border-color: #667eea;
    color: #667eea;
}

.filter-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.todo-stats {
    color: #64748b;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Todo List */
.todo-list-section {
    min-height: 300px;
    position: relative;
}

.todo-list {
    list-style: none;
}

.todo-item {
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.3s ease;
    animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.todo-item:hover {
    background: #f8fafc;
}

.todo-content {
    padding: 1.25rem 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.toggle-btn {
    width: 24px;
    height: 24px;
    border: 2px solid #e2e8f0;
    border-radius: 50%;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: transparent;
}

.toggle-btn:hover {
    border-color: #4ade80;
}

.todo-item.completed .toggle-btn {
    background: #4ade80;
    border-color: #4ade80;
    color: white;
}

.todo-text {
    flex: 1;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.todo-item.completed .todo-text {
    text-decoration: line-through;
    color: #94a3b8;
}

.todo-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.todo-item:hover .todo-actions {
    opacity: 1;
}

.edit-btn, .delete-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.edit-btn {
    background: #f59e0b;
    color: white;
}

.edit-btn:hover {
    background: #d97706;
    transform: scale(1.1);
}

.delete-btn {
    background: #ef4444;
    color: white;
}

.delete-btn:hover {
    background: #dc2626;
    transform: scale(1.1);
}

.edit-input {
    flex: 1;
    padding: 0.5rem;
    border: 2px solid #667eea;
    border-radius: 6px;
    font-size: 1rem;
    font-family: inherit;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #94a3b8;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #64748b;
}

.empty-state.hidden {
    display: none;
}

/* Actions Section */
.actions-section {
    padding: 1.5rem 2rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    text-align: center;
}

.clear-completed {
    padding: 0.75rem 1.5rem;
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.clear-completed:hover {
    background: #dc2626;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.clear-completed:disabled {
    background: #94a3b8;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Footer */
.app-footer {
    text-align: center;
    padding: 1rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-title {
        font-size: 2rem;
    }
    
    .todo-container {
        margin: 0 0.5rem;
        border-radius: 16px;
    }
    
    .add-todo-section,
    .filter-section,
    .actions-section {
        padding: 1.5rem;
    }
    
    .todo-content {
        padding: 1rem 1.5rem;
    }
    
    .filter-section {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .filter-buttons {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .input-group {
        flex-direction: column;
    }
    
    .add-btn {
        width: 100%;
        height: 50px;
    }
    
    .filter-buttons {
        flex-direction: column;
    }
    
    .filter-btn {
        justify-content: center;
    }
}
