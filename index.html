<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beautiful Todo App</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="fas fa-check-circle"></i>
                    Todo App
                </h1>
                <p class="app-subtitle">Stay organized and productive</p>
            </div>
        </header>

        <main class="main-content">
            <div class="todo-container">
                <!-- Add Todo Section -->
                <section class="add-todo-section">
                    <form class="add-todo-form" id="addTodoForm">
                        <div class="input-group">
                            <input 
                                type="text" 
                                id="todoInput" 
                                class="todo-input" 
                                placeholder="What needs to be done?"
                                maxlength="100"
                                required
                            >
                            <button type="submit" class="add-btn" aria-label="Add todo">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </form>
                </section>

                <!-- Filter Section -->
                <section class="filter-section">
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="all">
                            <i class="fas fa-list"></i>
                            All
                        </button>
                        <button class="filter-btn" data-filter="active">
                            <i class="fas fa-clock"></i>
                            Active
                        </button>
                        <button class="filter-btn" data-filter="completed">
                            <i class="fas fa-check"></i>
                            Completed
                        </button>
                    </div>
                    <div class="todo-stats">
                        <span id="todoCount">0 items</span>
                    </div>
                </section>

                <!-- Todo List Section -->
                <section class="todo-list-section">
                    <ul class="todo-list" id="todoList">
                        <!-- Todo items will be dynamically added here -->
                    </ul>
                    
                    <div class="empty-state" id="emptyState">
                        <div class="empty-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <h3>No todos yet</h3>
                        <p>Add a todo above to get started!</p>
                    </div>
                </section>

                <!-- Actions Section -->
                <section class="actions-section">
                    <button class="action-btn clear-completed" id="clearCompleted">
                        <i class="fas fa-trash"></i>
                        Clear Completed
                    </button>
                </section>
            </div>
        </main>

        <footer class="app-footer">
            <p>&copy; 2024 Beautiful Todo App. Made with ❤️</p>
        </footer>
    </div>

    <!-- Todo Item Template -->
    <template id="todoTemplate">
        <li class="todo-item" data-id="">
            <div class="todo-content">
                <button class="toggle-btn" aria-label="Toggle completion">
                    <i class="fas fa-check"></i>
                </button>
                <span class="todo-text"></span>
                <div class="todo-actions">
                    <button class="edit-btn" aria-label="Edit todo">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="delete-btn" aria-label="Delete todo">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <input type="text" class="edit-input" style="display: none;">
        </li>
    </template>

    <script src="script.js"></script>
</body>
</html>
